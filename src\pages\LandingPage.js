import React from "react";
import "./LandingPage.css";
import Navbar from "../layouts/Navbar";
import Hero from "../layouts/Hero";
import About from "../layouts/About";
import Projects from "../layouts/Projects";
import Experience from "../layouts/Experience";
import Contact from "../layouts/Contact";
import Footer from "../layouts/Footer";

const LandingPage = () => {
  // Mock data
  const personalInfo = {
    name: "<PERSON>",
    title: "Full Stack Developer",
    tagline: "Building digital experiences that make a difference",
    email: "<EMAIL>",
    phone: "+****************",
    location: "San Francisco, CA",
    profileImage:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
  };

  const skills = [
    "JavaScript",
    "React",
    "Node.js",
    "Python",
    "TypeScript",
    "MongoDB",
    "PostgreSQL",
    "AWS",
    "Docker",
    "Git",
  ];

  const projects = [
    {
      id: 1,
      title: "DAT Freight & Analytics",
      description:
        "US-based freight exchange service and provider of transportation information serving North America. Worked as a Full-Stack dev, updating and improving their current API, adding new user flows, fixing the responsive design, allowing the users to select the best freight forwarder that suits their needs. Also improved their error handling and monitoring with Sentry",
      technologies: [
        "Angular",
        "NodeJS",
        "Sass",
        "Sentry",
        "MySQL",
        "Docker",
        "MongoDB",
        "AWS Batch",
      ],
      image:
        "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=250&fit=crop",
      liveUrl: "https://www.dat.com/",
      githubUrl: "#",
    },
    {
      id: 2,
      title: "Compare Credit",
      description:
        "Company that specializes in credit card comparison services. On the frontend, developed and maintained a website that enables users to compare credit cards options from multiple providers. On the backend, utilized Node, Sanity CMS, Google BigQuery for the database service, and integrated Sentry for error monitoring and Segment for analytics and tracking.",
      technologies: [
        "Jest",
        "NextJS",
        "TailwindCSS",
        "Node",
        "React",
        "Lodash",
        "Sanity",
        "Google BigQuery",
        "Sentry",
        "Segment",
      ],
      image:
        "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=250&fit=crop",
      liveUrl: "https://comparecredit.com/",
      githubUrl: "#",
    },
    {
      id: 3,
      title: "BusinessLoans",
      description:
        "Website that offers comparisons between different loans from several companies. The user has to fill a short form about themselves, after which it is presented with a selection of loan offers that they can evaluate and choose the best fit. Uses NextJs has it main technology, is highly integrated with their CMS using Sanity, and also connects to Google Cloud Big Query for the Database service",
      technologies: [
        "NextJs",
        "Node",
        "React",
        "Typescript",
        "Jest",
        "TailwindCSS",
        "Google Cloud Big Query",
      ],
      image:
        "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop",
      liveUrl: "https://businessloans.com/",
      githubUrl: "#",
    },
    {
      id: 4,
      title: "IQVIA Salesforce Application",
      description:
        "Application created and hosted on Salesforce platform, using Lightning Web Components (LWC) for reusability and better code delegation. Created custom LWC to fit the clients needs, updating existing LWC, fixing bugs, changing styles according to the UI/UX team proposals, creating new pages and applications flows following the designers Zeplin assets.",
      technologies: [
        "Javascript",
        "SASS",
        "Lightning Web Components",
        "Lightning Design System",
        "CSS",
        "NodeJS",
      ],
      image:
        "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop",
      liveUrl: "https://ivp-dit0.my.salesforce.com/",
      githubUrl: "#",
    },
    {
      id: 5,
      title: "Gdobo CLI Tool",
      description:
        "Command Line Interface (CLI) tool written in Nodejs, which connects to the Google Drive API to compare the files between a local folder and a folder in Google Drive. Uses the Nodejs Stream API to read and write the files. Allows the user to upload, download, remove and update the files both locally and on Google Drive storage.",
      technologies: [
        "NodeJs",
        "Javascript",
        "Google Drive API",
        "Google OAuth API",
        "Bluebird",
        "Lodash",
      ],
      image:
        "https://images.unsplash.com/photo-1629654297299-c8506221ca97?w=400&h=250&fit=crop",
      liveUrl: "#",
      githubUrl: "https://github.com/ZoiloGranda/gdobo",
    },
    {
      id: 6,
      title: "Connexient Hospital Navigation",
      description:
        "Around 50 Web applications for indoor hospitals navigation and exploration, find places, get step by step directions and many other features. Worked on the front-end team, creating new releases of the web and kiosks apps for new hospitals, fixing bugs and adding new features on existing projects. The web app is written on vanilla javascript, and for the navigation we used the VisioGlobe products solutions.",
      technologies: [
        "Javascript",
        "JQuery",
        "VisioGlobe Web & Kiosk",
        "Google Maps API",
        "jsPDF",
        "Less",
        "Bootstrap",
      ],
      image:
        "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=250&fit=crop",
      liveUrl: "https://capefear-dev.connexient.com/web/",
      githubUrl: "#",
    },
  ];

  const experience = [
    {
      id: 1,
      company: "TechCorp Solutions",
      position: "Senior Full Stack Developer",
      duration: "2022 - Present",
      description: "Lead development of web applications serving 100k+ users",
    },
    {
      id: 2,
      company: "StartupXYZ",
      position: "Frontend Developer",
      duration: "2020 - 2022",
      description:
        "Built responsive web applications using React and modern JavaScript",
    },
    {
      id: 3,
      company: "Digital Agency",
      position: "Junior Developer",
      duration: "2019 - 2020",
      description:
        "Developed client websites and learned full-stack development",
    },
  ];

  return (
    <div className="landing-page">
      <Navbar personalInfo={personalInfo} />

      <Hero personalInfo={personalInfo} />

      <About skills={skills} />

      <Projects projects={projects} />

      <Experience experience={experience} />

      <Contact personalInfo={personalInfo} />

      <Footer personalInfo={personalInfo} />
    </div>
  );
};

export default LandingPage;
